import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { FormField } from '../FormField';

describe('FormField - Text Input', () => {
  it('renders text input with label', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="text"
        label="Name"
        value="John Doe"
        onChange={mockOnChange}
      />
    );
    
    expect(screen.getByLabelText('Name')).toBeInTheDocument();
    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
  });

  it('handles text input changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={mockOnChange}
      />
    );
    
    const input = screen.getByLabelText('Name');
    fireEvent.change(input, { target: { value: 'New Value' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('New Value');
  });

  it('shows required indicator', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        required={true}
      />
    );
    
    expect(screen.getByText('*')).toBeInTheDocument();
  });

  it('displays error message', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        error="This field is required"
      />
    );
    
    expect(screen.getByText('This field is required')).toBeInTheDocument();
  });

  it('displays description', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        description="Enter your full name"
      />
    );
    
    expect(screen.getByText('Enter your full name')).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        disabled={true}
      />
    );
    
    const input = screen.getByLabelText('Name');
    expect(input).toBeDisabled();
  });
});

describe('FormField - Email Input', () => {
  it('renders email input', () => {
    render(
      <FormField
        type="email"
        label="Email"
        value="<EMAIL>"
        onChange={() => {}}
      />
    );
    
    const input = screen.getByLabelText('Email');
    expect(input).toHaveAttribute('type', 'email');
    expect(input).toHaveValue('<EMAIL>');
  });
});

describe('FormField - Textarea', () => {
  it('renders textarea', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="textarea"
        label="Description"
        value="Some text"
        onChange={mockOnChange}
        rows={5}
      />
    );
    
    const textarea = screen.getByLabelText('Description');
    expect(textarea.tagName).toBe('TEXTAREA');
    expect(textarea).toHaveValue('Some text');
    expect(textarea).toHaveAttribute('rows', '5');
  });

  it('handles textarea changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="textarea"
        label="Description"
        value=""
        onChange={mockOnChange}
      />
    );
    
    const textarea = screen.getByLabelText('Description');
    fireEvent.change(textarea, { target: { value: 'New description' } });
    
    expect(mockOnChange).toHaveBeenCalledWith('New description');
  });
});

describe('FormField - Select', () => {
  const options = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' }
  ];

  it('renders select with options', () => {
    render(
      <FormField
        type="select"
        label="Category"
        value="option1"
        onChange={() => {}}
        options={options}
      />
    );
    
    expect(screen.getByText('Category')).toBeInTheDocument();
    // Select component testing would require more specific setup
  });

  it('handles select changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="select"
        label="Category"
        value=""
        onChange={mockOnChange}
        options={options}
      />
    );
    
    // Would need to test select interaction
    // This depends on the specific Select component implementation
  });

  it('shows placeholder in select', () => {
    render(
      <FormField
        type="select"
        label="Category"
        value=""
        onChange={() => {}}
        options={options}
        placeholder="Choose an option"
      />
    );
    
    // Would check for placeholder text
  });
});

describe('FormField - Date', () => {
  it('renders date picker', () => {
    const mockOnChange = vi.fn();
    const testDate = new Date('2024-01-01');
    
    render(
      <FormField
        type="date"
        label="Due Date"
        value={testDate}
        onChange={mockOnChange}
      />
    );
    
    expect(screen.getByText('Due Date')).toBeInTheDocument();
    // DatePicker component testing would require more specific setup
  });

  it('handles date changes', () => {
    const mockOnChange = vi.fn();
    
    render(
      <FormField
        type="date"
        label="Due Date"
        value={undefined}
        onChange={mockOnChange}
      />
    );
    
    // Would need to test date picker interaction
  });

  it('disables past dates when specified', () => {
    render(
      <FormField
        type="date"
        label="Due Date"
        value={undefined}
        onChange={() => {}}
        disablePastDates={true}
      />
    );
    
    // Would check that past dates are disabled
  });
});

describe('FormField - Styling and Classes', () => {
  it('applies custom className', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        className="custom-field"
      />
    );
    
    const container = screen.getByText('Name').closest('div');
    expect(container).toHaveClass('custom-field');
  });

  it('applies error styling when error is present', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        error="Required field"
      />
    );
    
    const input = screen.getByLabelText('Name');
    expect(input).toHaveClass('border-red-500');
  });

  it('has proper spacing classes', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
      />
    );
    
    const container = screen.getByText('Name').closest('div');
    expect(container).toHaveClass('space-y-2');
  });
});

describe('FormField - Accessibility', () => {
  it('associates label with input', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
      />
    );
    
    const input = screen.getByLabelText('Name');
    const label = screen.getByText('Name');
    
    expect(input).toBeInTheDocument();
    expect(label).toBeInTheDocument();
  });

  it('associates error message with input', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        error="This field is required"
      />
    );
    
    const input = screen.getByLabelText('Name');
    const errorMessage = screen.getByText('This field is required');
    
    expect(input).toHaveAttribute('aria-describedby');
    expect(errorMessage).toBeInTheDocument();
  });

  it('marks required fields appropriately', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
        required={true}
      />
    );
    
    const input = screen.getByLabelText('Name');
    expect(input).toHaveAttribute('required');
  });
});

describe('FormField - Edge Cases', () => {
  it('handles missing label', () => {
    render(
      <FormField
        type="text"
        value=""
        onChange={() => {}}
      />
    );
    
    const input = screen.getByRole('textbox');
    expect(input).toBeInTheDocument();
  });

  it('handles empty options array for select', () => {
    render(
      <FormField
        type="select"
        label="Category"
        value=""
        onChange={() => {}}
        options={[]}
      />
    );
    
    expect(screen.getByText('Category')).toBeInTheDocument();
  });

  it('handles undefined value gracefully', () => {
    render(
      <FormField
        type="text"
        label="Name"
        value=""
        onChange={() => {}}
      />
    );
    
    const input = screen.getByLabelText('Name');
    expect(input).toHaveValue('');
  });
});
