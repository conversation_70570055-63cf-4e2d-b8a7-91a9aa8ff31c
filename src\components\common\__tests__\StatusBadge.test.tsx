import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { StatusBadge } from '../StatusBadge';

describe('StatusBadge', () => {
  it('renders with basic status', () => {
    render(<StatusBadge status="Completed" />);
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  it('applies correct color classes for different statuses', () => {
    const { rerender } = render(<StatusBadge status="Completed" />);
    let badge = screen.getByText('Completed');
    expect(badge).toHaveClass('bg-green-100', 'text-green-800');

    rerender(<StatusBadge status="In Progress" />);
    badge = screen.getByText('In Progress');
    expect(badge).toHaveClass('bg-blue-100', 'text-blue-800');

    rerender(<StatusBadge status="Overdue" />);
    badge = screen.getByText('Overdue');
    expect(badge).toHaveClass('bg-red-100', 'text-red-800');
  });

  it('shows icon when showIcon is true', () => {
    render(<StatusBadge status="Completed" showIcon={true} />);
    const icon = screen.getByRole('img', { hidden: true });
    expect(icon).toBeInTheDocument();
  });

  it('applies correct size classes', () => {
    const { rerender } = render(<StatusBadge status="Test" size="sm" />);
    let badge = screen.getByText('Test');
    expect(badge).toHaveClass('text-xs', 'px-2', 'py-0.5');

    rerender(<StatusBadge status="Test" size="lg" />);
    badge = screen.getByText('Test');
    expect(badge).toHaveClass('text-base', 'px-3', 'py-1');
  });

  it('applies custom className', () => {
    render(<StatusBadge status="Test" className="custom-class" />);
    const badge = screen.getByText('Test');
    expect(badge).toHaveClass('custom-class');
  });

  it('uses correct variant', () => {
    render(<StatusBadge status="Test" variant="destructive" />);
    const badge = screen.getByText('Test');
    expect(badge).toHaveClass('border-transparent', 'bg-destructive');
  });

  it('handles different status types correctly', () => {
    const statuses = [
      'completed',
      'in progress', 
      'pending',
      'overdue',
      'cancelled'
    ];

    statuses.forEach(status => {
      const { unmount } = render(<StatusBadge status={status} />);
      expect(screen.getByText(status)).toBeInTheDocument();
      unmount();
    });
  });

  it('renders with proper accessibility attributes', () => {
    render(<StatusBadge status="Completed" />);
    const badge = screen.getByText('Completed');
    expect(badge).toHaveAttribute('role', 'status');
  });
});

describe('StatusBadge Icon Logic', () => {
  it('shows correct icon for completed status', () => {
    render(<StatusBadge status="Completed" showIcon={true} />);
    // Check for CheckCircle icon (would need to mock lucide-react for exact icon testing)
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('shows correct icon for in progress status', () => {
    render(<StatusBadge status="In Progress" showIcon={true} />);
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('shows correct icon for pending status', () => {
    render(<StatusBadge status="Pending" showIcon={true} />);
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('shows correct icon for overdue status', () => {
    render(<StatusBadge status="Overdue" showIcon={true} />);
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('shows correct icon for cancelled status', () => {
    render(<StatusBadge status="Cancelled" showIcon={true} />);
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });

  it('does not show icon when showIcon is false', () => {
    render(<StatusBadge status="Completed" showIcon={false} />);
    expect(screen.queryByRole('img', { hidden: true })).not.toBeInTheDocument();
  });
});

describe('StatusBadge Edge Cases', () => {
  it('handles empty status', () => {
    render(<StatusBadge status="" />);
    expect(screen.getByText('')).toBeInTheDocument();
  });

  it('handles unknown status', () => {
    render(<StatusBadge status="Unknown Status" />);
    expect(screen.getByText('Unknown Status')).toBeInTheDocument();
    const badge = screen.getByText('Unknown Status');
    expect(badge).toHaveClass('bg-gray-100', 'text-gray-800');
  });

  it('handles case insensitive status matching', () => {
    render(<StatusBadge status="COMPLETED" showIcon={true} />);
    expect(screen.getByText('COMPLETED')).toBeInTheDocument();
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
  });
});
