import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import MilestoneTab from '../MilestoneTab';

// Mock the milestone components
vi.mock('../milestone', () => ({
  MilestoneCard: ({ milestone, onEdit, onDelete, onAddTask }: any) => (
    <div data-testid={`milestone-${milestone.id}`}>
      <h3>{milestone.name}</h3>
      <p>{milestone.description}</p>
      <span>Status: {milestone.status}</span>
      <span>Progress: {milestone.progress}%</span>
      <button onClick={() => onEdit(milestone)}>Edit Milestone</button>
      <button onClick={() => onDelete(milestone.id)}>Delete Milestone</button>
      <button onClick={() => onAddTask(milestone.id)}>Add Task</button>
      <div data-testid={`tasks-${milestone.id}`}>
        {milestone.tasks.map((task: any) => (
          <div key={task.id} data-testid={`task-${task.id}`}>
            <span>{task.title}</span>
            <span>Status: {task.status}</span>
          </div>
        ))}
      </div>
    </div>
  ),
  MilestoneDialog: ({ isOpen, onClose, onSave, editingMilestone }: any) => (
    isOpen ? (
      <div data-testid="milestone-dialog">
        <h2>{editingMilestone ? 'Edit Milestone' : 'Create New Milestone'}</h2>
        <button onClick={onClose}>Cancel</button>
        <button onClick={onSave}>Save</button>
      </div>
    ) : null
  ),
  TaskDialog: ({ isOpen, onClose, onSave, editingTask }: any) => (
    isOpen ? (
      <div data-testid="task-dialog">
        <h2>{editingTask ? 'Edit Task' : 'Create New Task'}</h2>
        <button onClick={onClose}>Cancel</button>
        <button onClick={onSave}>Save</button>
      </div>
    ) : null
  ),
}));

// Mock toast
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
};

vi.mock('sonner', () => ({
  toast: mockToast,
}));

describe('MilestoneTab Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders milestone statistics correctly', async () => {
    render(<MilestoneTab />);
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Project Milestones')).toBeInTheDocument();
    });

    // Check statistics cards
    expect(screen.getByText('Completed')).toBeInTheDocument();
    expect(screen.getByText('In Progress')).toBeInTheDocument();
    expect(screen.getByText('Not Started')).toBeInTheDocument();
    expect(screen.getByText('Total Tasks')).toBeInTheDocument();
  });

  it('displays milestones after loading', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Literature Review')).toBeInTheDocument();
      expect(screen.getByText('Data Collection')).toBeInTheDocument();
    });

    // Check milestone details
    expect(screen.getByText('Complete comprehensive literature review on machine learning applications')).toBeInTheDocument();
    expect(screen.getByText('Gather and prepare datasets for machine learning experiments')).toBeInTheDocument();
  });

  it('shows add milestone button for authorized users', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Add Milestone')).toBeInTheDocument();
    });
  });

  it('opens milestone dialog when add milestone is clicked', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      const addButton = screen.getByText('Add Milestone');
      fireEvent.click(addButton);
    });

    expect(screen.getByTestId('milestone-dialog')).toBeInTheDocument();
    expect(screen.getByText('Create New Milestone')).toBeInTheDocument();
  });

  it('handles milestone editing', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Literature Review')).toBeInTheDocument();
    });

    const editButton = screen.getByText('Edit Milestone');
    fireEvent.click(editButton);

    expect(screen.getByTestId('milestone-dialog')).toBeInTheDocument();
    expect(screen.getByText('Edit Milestone')).toBeInTheDocument();
  });

  it('handles milestone deletion', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Literature Review')).toBeInTheDocument();
    });

    const deleteButton = screen.getByText('Delete Milestone');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('Milestone deleted successfully');
    });
  });

  it('opens task dialog when add task is clicked', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Literature Review')).toBeInTheDocument();
    });

    const addTaskButton = screen.getByText('Add Task');
    fireEvent.click(addTaskButton);

    expect(screen.getByTestId('task-dialog')).toBeInTheDocument();
    expect(screen.getByText('Create New Task')).toBeInTheDocument();
  });

  it('displays tasks within milestones', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByTestId('milestone-1')).toBeInTheDocument();
    });

    // Check that tasks are displayed
    const tasksContainer = screen.getByTestId('tasks-1');
    expect(tasksContainer).toBeInTheDocument();
    
    expect(screen.getByText('Research paper collection')).toBeInTheDocument();
    expect(screen.getByText('Literature analysis')).toBeInTheDocument();
  });

  it('calculates and displays progress correctly', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Progress: 100%')).toBeInTheDocument(); // Literature Review
      expect(screen.getByText('Progress: 60%')).toBeInTheDocument(); // Data Collection
    });
  });

  it('handles loading state', () => {
    render(<MilestoneTab />);
    
    // Initially should show loading or empty state
    expect(screen.getByText('Project Milestones')).toBeInTheDocument();
  });

  it('handles empty milestones state', async () => {
    // Mock empty milestones
    vi.doMock('../MilestoneTab', () => {
      return {
        default: () => (
          <div>
            <h2>Project Milestones</h2>
            <p>No milestones created yet. Create your first milestone to start tracking progress.</p>
          </div>
        ),
      };
    });

    const { default: EmptyMilestoneTab } = await import('../MilestoneTab');
    render(<EmptyMilestoneTab />);
    
    expect(screen.getByText('No milestones created yet. Create your first milestone to start tracking progress.')).toBeInTheDocument();
  });

  it('closes dialogs when cancel is clicked', async () => {
    render(<MilestoneTab />);
    
    // Open milestone dialog
    await waitFor(() => {
      const addButton = screen.getByText('Add Milestone');
      fireEvent.click(addButton);
    });

    expect(screen.getByTestId('milestone-dialog')).toBeInTheDocument();

    // Close dialog
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByTestId('milestone-dialog')).not.toBeInTheDocument();
    });
  });

  it('handles milestone creation', async () => {
    render(<MilestoneTab />);
    
    // Open milestone dialog
    await waitFor(() => {
      const addButton = screen.getByText('Add Milestone');
      fireEvent.click(addButton);
    });

    // Save milestone
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('Milestone created successfully');
    });
  });

  it('handles task creation', async () => {
    render(<MilestoneTab />);
    
    // Wait for milestones to load and open task dialog
    await waitFor(() => {
      const addTaskButton = screen.getByText('Add Task');
      fireEvent.click(addTaskButton);
    });

    // Save task
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockToast.success).toHaveBeenCalledWith('Task created successfully');
    });
  });

  it('displays correct milestone and task counts in statistics', async () => {
    render(<MilestoneTab />);
    
    await waitFor(() => {
      // Should show statistics based on mock data
      // 1 completed milestone, 1 in progress, 0 not started
      // Total of 5 tasks across all milestones
      expect(screen.getByText('1')).toBeInTheDocument(); // Completed milestones
      expect(screen.getByText('5')).toBeInTheDocument(); // Total tasks
    });
  });

  it('handles error states gracefully', async () => {
    // Mock error scenario
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<MilestoneTab />);
    
    // Component should still render even if there are errors
    expect(screen.getByText('Project Milestones')).toBeInTheDocument();
    
    consoleSpy.mockRestore();
  });
});

describe('MilestoneTab User Permissions', () => {
  it('shows add milestone button for PI users', async () => {
    // Mock PI user
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      user: { role: 'PI', email: '<EMAIL>' },
      isAuthenticated: true,
    });

    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Add Milestone')).toBeInTheDocument();
    });
  });

  it('shows add milestone button for Leader users', async () => {
    // Mock Leader user
    vi.mocked(require('@/contexts/AuthContext').useAuth).mockReturnValue({
      user: { role: 'Leader', email: '<EMAIL>' },
      isAuthenticated: true,
    });

    render(<MilestoneTab />);
    
    await waitFor(() => {
      expect(screen.getByText('Add Milestone')).toBeInTheDocument();
    });
  });
});
