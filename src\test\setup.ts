import '@testing-library/jest-dom';
import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
});

// Mock HTMLElement.scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  writable: true,
  value: vi.fn(),
});

// Mock Lucide React icons
vi.mock('lucide-react', () => ({
  CheckCircle: () => <div role="img" aria-label="check-circle" />,
  Clock: () => <div role="img" aria-label="clock" />,
  AlertTriangle: () => <div role="img" aria-label="alert-triangle" />,
  AlertCircle: () => <div role="img" aria-label="alert-circle" />,
  XCircle: () => <div role="img" aria-label="x-circle" />,
  User: () => <div role="img" aria-label="user" />,
  Edit: () => <div role="img" aria-label="edit" />,
  Trash2: () => <div role="img" aria-label="trash" />,
  Plus: () => <div role="img" aria-label="plus" />,
  Search: () => <div role="img" aria-label="search" />,
  Filter: () => <div role="img" aria-label="filter" />,
  Calendar: () => <div role="img" aria-label="calendar" />,
  Upload: () => <div role="img" aria-label="upload" />,
  File: () => <div role="img" aria-label="file" />,
  X: () => <div role="img" aria-label="x" />,
}));

// Mock date-fns if used
vi.mock('date-fns', () => ({
  format: vi.fn((date, formatStr) => date.toISOString()),
  isValid: vi.fn(() => true),
  parseISO: vi.fn((dateStr) => new Date(dateStr)),
}));

// Mock React Router if used
vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/' }),
  useParams: () => ({}),
  Link: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
  NavLink: ({ children, to, ...props }: any) => (
    <a href={to} {...props}>
      {children}
    </a>
  ),
}));

// Mock Sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

// Mock AuthContext
vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'user',
    },
    isAuthenticated: true,
    login: vi.fn(),
    logout: vi.fn(),
  }),
}));

// Mock shared utils
vi.mock('@/shared/utils/helpers', () => ({
  formatDate: vi.fn((date) => new Date(date).toLocaleDateString()),
  formatDateTime: vi.fn((date) => new Date(date).toLocaleString()),
  formatCurrency: vi.fn((amount) => `$${amount.toLocaleString()}`),
  formatFileSize: vi.fn((bytes) => `${bytes} bytes`),
  getStatusColor: vi.fn((status) => {
    const statusLower = status.toLowerCase();
    if (statusLower.includes('completed') || statusLower.includes('complete')) {
      return 'bg-green-100 text-green-800';
    }
    if (statusLower.includes('progress') || statusLower.includes('processing')) {
      return 'bg-blue-100 text-blue-800';
    }
    if (statusLower.includes('pending') || statusLower.includes('waiting')) {
      return 'bg-yellow-100 text-yellow-800';
    }
    if (statusLower.includes('overdue') || statusLower.includes('late')) {
      return 'bg-red-100 text-red-800';
    }
    return 'bg-gray-100 text-gray-800';
  }),
  generateId: vi.fn(() => 'test-id-123'),
  validateEmail: vi.fn((email) => /\S+@\S+\.\S+/.test(email)),
  calculateProgress: vi.fn((completed, total) => Math.round((completed / total) * 100)),
}));

// Custom render function for testing with providers
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';

// Mock providers wrapper
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <div data-testid="providers-wrapper">{children}</div>;
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
