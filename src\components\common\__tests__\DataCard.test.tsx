import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { DataCard } from '../DataCard';
import { User } from 'lucide-react';

describe('DataCard', () => {
  it('renders with basic props', () => {
    render(<DataCard title="Test Card" />);
    expect(screen.getByText('Test Card')).toBeInTheDocument();
  });

  it('renders with description', () => {
    render(
      <DataCard 
        title="Test Card" 
        description="This is a test description" 
      />
    );
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('This is a test description')).toBeInTheDocument();
  });

  it('renders with status badge', () => {
    render(
      <DataCard 
        title="Test Card" 
        status="Completed" 
      />
    );
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Completed')).toBeInTheDocument();
  });

  it('renders with icon', () => {
    render(
      <DataCard 
        title="Test Card" 
        icon={User}
      />
    );
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    // Icon would be rendered but testing exact icon requires mocking
  });

  it('renders with badges', () => {
    const badges = [
      { label: 'High Priority', variant: 'destructive' as const },
      { label: 'Urgent', variant: 'secondary' as const }
    ];
    
    render(
      <DataCard 
        title="Test Card" 
        badges={badges}
      />
    );
    
    expect(screen.getByText('High Priority')).toBeInTheDocument();
    expect(screen.getByText('Urgent')).toBeInTheDocument();
  });

  it('renders with metadata in detailed variant', () => {
    const metadata = [
      { label: 'Created', value: '2024-01-01' },
      { label: 'Author', value: 'John Doe' }
    ];
    
    render(
      <DataCard 
        title="Test Card" 
        variant="detailed"
        metadata={metadata}
      />
    );
    
    expect(screen.getByText('Created:')).toBeInTheDocument();
    expect(screen.getByText('2024-01-01')).toBeInTheDocument();
    expect(screen.getByText('Author:')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('renders with actions', () => {
    const mockEdit = vi.fn();
    const mockDelete = vi.fn();
    
    const actions = [
      { label: 'Edit', onClick: mockEdit },
      { label: 'Delete', onClick: mockDelete, variant: 'destructive' as const }
    ];
    
    render(
      <DataCard 
        title="Test Card" 
        actions={actions}
      />
    );
    
    const editButton = screen.getByText('Edit');
    const deleteButton = screen.getByText('Delete');
    
    expect(editButton).toBeInTheDocument();
    expect(deleteButton).toBeInTheDocument();
    
    fireEvent.click(editButton);
    expect(mockEdit).toHaveBeenCalledTimes(1);
    
    fireEvent.click(deleteButton);
    expect(mockDelete).toHaveBeenCalledTimes(1);
  });

  it('handles click when clickable', () => {
    const mockClick = vi.fn();
    
    render(
      <DataCard 
        title="Test Card" 
        clickable={true}
        onClick={mockClick}
      />
    );
    
    const card = screen.getByText('Test Card').closest('[role="button"]') || 
                 screen.getByText('Test Card').closest('div');
    
    if (card) {
      fireEvent.click(card);
      expect(mockClick).toHaveBeenCalledTimes(1);
    }
  });

  it('applies correct variant classes', () => {
    const { rerender } = render(<DataCard title="Test" variant="compact" />);
    // Test compact variant (would need to check specific classes)
    
    rerender(<DataCard title="Test" variant="detailed" />);
    // Test detailed variant
    
    rerender(<DataCard title="Test" variant="default" />);
    // Test default variant
  });

  it('renders children content', () => {
    render(
      <DataCard title="Test Card">
        <div>Custom content</div>
      </DataCard>
    );
    
    expect(screen.getByText('Custom content')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <DataCard 
        title="Test Card" 
        className="custom-class"
      />
    );
    
    const card = screen.getByText('Test Card').closest('div');
    expect(card).toHaveClass('custom-class');
  });

  it('shows hover effects when clickable', () => {
    render(
      <DataCard 
        title="Test Card" 
        clickable={true}
        onClick={() => {}}
      />
    );
    
    const card = screen.getByText('Test Card').closest('div');
    expect(card).toHaveClass('cursor-pointer');
  });

  it('disables actions when specified', () => {
    const mockAction = vi.fn();
    
    const actions = [
      { label: 'Disabled Action', onClick: mockAction, disabled: true }
    ];
    
    render(
      <DataCard 
        title="Test Card" 
        actions={actions}
      />
    );
    
    const button = screen.getByText('Disabled Action');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(mockAction).not.toHaveBeenCalled();
  });
});

describe('DataCard Accessibility', () => {
  it('has proper heading structure', () => {
    render(<DataCard title="Test Card" />);
    const heading = screen.getByRole('heading');
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent('Test Card');
  });

  it('has proper button roles for actions', () => {
    const actions = [
      { label: 'Edit', onClick: () => {} },
      { label: 'Delete', onClick: () => {} }
    ];
    
    render(
      <DataCard 
        title="Test Card" 
        actions={actions}
      />
    );
    
    const buttons = screen.getAllByRole('button');
    expect(buttons).toHaveLength(2);
    expect(buttons[0]).toHaveTextContent('Edit');
    expect(buttons[1]).toHaveTextContent('Delete');
  });

  it('supports keyboard navigation for clickable cards', () => {
    const mockClick = vi.fn();
    
    render(
      <DataCard 
        title="Test Card" 
        clickable={true}
        onClick={mockClick}
      />
    );
    
    const card = screen.getByText('Test Card').closest('div');
    
    if (card) {
      fireEvent.keyDown(card, { key: 'Enter' });
      // Would need to implement keyboard handling in component
    }
  });
});

describe('DataCard Edge Cases', () => {
  it('handles empty actions array', () => {
    render(
      <DataCard 
        title="Test Card" 
        actions={[]}
      />
    );
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('handles empty metadata array', () => {
    render(
      <DataCard 
        title="Test Card" 
        variant="detailed"
        metadata={[]}
      />
    );
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    // Should not render metadata section
  });

  it('handles missing optional props', () => {
    render(<DataCard title="Test Card" />);
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.queryByText('undefined')).not.toBeInTheDocument();
  });
});
