import { describe, it, expect } from 'vitest';
import {
  formatDate,
  formatDateTime,
  formatCurrency,
  formatFileSize,
  getStatusColor,
  generateId,
  validateEmail,
  calculateProgress,
  truncateText,
  capitalizeFirst,
  slugify,
  debounce,
  isValidUrl,
  parseQueryParams,
  formatPhoneNumber,
  getInitials,
  sortByProperty,
  groupByProperty,
  removeDuplicates,
  deepClone,
  isEmptyObject,
  mergeObjects,
} from '../helpers';

describe('Date and Time Utilities', () => {
  it('formats date correctly', () => {
    const date = new Date('2024-01-15T10:30:00Z');
    expect(formatDate(date)).toBe('Jan 15, 2024');
    expect(formatDate(date, 'yyyy-MM-dd')).toBe('2024-01-15');
  });

  it('formats date time correctly', () => {
    const date = new Date('2024-01-15T10:30:00Z');
    expect(formatDateTime(date)).toContain('Jan 15, 2024');
    expect(formatDateTime(date)).toContain('10:30');
  });

  it('handles invalid dates', () => {
    expect(formatDate(new Date('invalid'))).toBe('Invalid Date');
    expect(formatDateTime(new Date('invalid'))).toBe('Invalid Date');
  });
});

describe('Currency and Number Utilities', () => {
  it('formats currency correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56');
    expect(formatCurrency(0)).toBe('$0.00');
    expect(formatCurrency(1000000)).toBe('$1,000,000.00');
  });

  it('formats currency with different currencies', () => {
    expect(formatCurrency(1234.56, 'EUR')).toBe('€1,234.56');
    expect(formatCurrency(1234.56, 'GBP')).toBe('£1,234.56');
  });

  it('formats file size correctly', () => {
    expect(formatFileSize(1024)).toBe('1.0 KB');
    expect(formatFileSize(1048576)).toBe('1.0 MB');
    expect(formatFileSize(1073741824)).toBe('1.0 GB');
    expect(formatFileSize(500)).toBe('500 B');
  });

  it('calculates progress correctly', () => {
    expect(calculateProgress(5, 10)).toBe(50);
    expect(calculateProgress(0, 10)).toBe(0);
    expect(calculateProgress(10, 10)).toBe(100);
    expect(calculateProgress(3, 7)).toBe(43); // Rounded
  });

  it('handles edge cases for progress calculation', () => {
    expect(calculateProgress(5, 0)).toBe(0); // Division by zero
    expect(calculateProgress(-1, 10)).toBe(0); // Negative completed
    expect(calculateProgress(15, 10)).toBe(100); // Over 100%
  });
});

describe('Status and Color Utilities', () => {
  it('returns correct colors for status', () => {
    expect(getStatusColor('completed')).toBe('bg-green-100 text-green-800');
    expect(getStatusColor('Completed')).toBe('bg-green-100 text-green-800');
    expect(getStatusColor('COMPLETED')).toBe('bg-green-100 text-green-800');
    
    expect(getStatusColor('in progress')).toBe('bg-blue-100 text-blue-800');
    expect(getStatusColor('processing')).toBe('bg-blue-100 text-blue-800');
    
    expect(getStatusColor('pending')).toBe('bg-yellow-100 text-yellow-800');
    expect(getStatusColor('waiting')).toBe('bg-yellow-100 text-yellow-800');
    
    expect(getStatusColor('overdue')).toBe('bg-red-100 text-red-800');
    expect(getStatusColor('late')).toBe('bg-red-100 text-red-800');
    
    expect(getStatusColor('unknown status')).toBe('bg-gray-100 text-gray-800');
  });
});

describe('String Utilities', () => {
  it('truncates text correctly', () => {
    const longText = 'This is a very long text that should be truncated';
    expect(truncateText(longText, 20)).toBe('This is a very long...');
    expect(truncateText('Short text', 20)).toBe('Short text');
    expect(truncateText(longText, 20, '---')).toBe('This is a very long---');
  });

  it('capitalizes first letter', () => {
    expect(capitalizeFirst('hello world')).toBe('Hello world');
    expect(capitalizeFirst('HELLO WORLD')).toBe('HELLO WORLD');
    expect(capitalizeFirst('')).toBe('');
    expect(capitalizeFirst('a')).toBe('A');
  });

  it('creates slugs correctly', () => {
    expect(slugify('Hello World')).toBe('hello-world');
    expect(slugify('This is a Test!')).toBe('this-is-a-test');
    expect(slugify('Special@Characters#Here')).toBe('specialcharactershere');
    expect(slugify('Multiple   Spaces')).toBe('multiple-spaces');
  });

  it('gets initials correctly', () => {
    expect(getInitials('John Doe')).toBe('JD');
    expect(getInitials('John Michael Doe')).toBe('JMD');
    expect(getInitials('John')).toBe('J');
    expect(getInitials('')).toBe('');
    expect(getInitials('john doe')).toBe('JD'); // Should capitalize
  });
});

describe('Validation Utilities', () => {
  it('validates email correctly', () => {
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('<EMAIL>')).toBe(true);
    expect(validateEmail('invalid.email')).toBe(false);
    expect(validateEmail('test@')).toBe(false);
    expect(validateEmail('@example.com')).toBe(false);
    expect(validateEmail('')).toBe(false);
  });

  it('validates URLs correctly', () => {
    expect(isValidUrl('https://example.com')).toBe(true);
    expect(isValidUrl('http://example.com')).toBe(true);
    expect(isValidUrl('https://sub.example.com/path')).toBe(true);
    expect(isValidUrl('example.com')).toBe(false);
    expect(isValidUrl('not-a-url')).toBe(false);
    expect(isValidUrl('')).toBe(false);
  });
});

describe('ID Generation', () => {
  it('generates unique IDs', () => {
    const id1 = generateId();
    const id2 = generateId();
    
    expect(id1).toBeTruthy();
    expect(id2).toBeTruthy();
    expect(id1).not.toBe(id2);
    expect(typeof id1).toBe('string');
    expect(id1.length).toBeGreaterThan(0);
  });

  it('generates IDs with prefix', () => {
    const id = generateId('user');
    expect(id).toMatch(/^user-/);
  });
});

describe('Phone Number Formatting', () => {
  it('formats phone numbers correctly', () => {
    expect(formatPhoneNumber('1234567890')).toBe('(*************');
    expect(formatPhoneNumber('************')).toBe('(*************');
    expect(formatPhoneNumber('(*************')).toBe('(*************');
    expect(formatPhoneNumber('123')).toBe('123'); // Too short
  });
});

describe('Query Parameter Utilities', () => {
  it('parses query parameters correctly', () => {
    expect(parseQueryParams('?name=John&age=30')).toEqual({
      name: 'John',
      age: '30'
    });
    
    expect(parseQueryParams('name=John&age=30')).toEqual({
      name: 'John',
      age: '30'
    });
    
    expect(parseQueryParams('')).toEqual({});
    expect(parseQueryParams('?')).toEqual({});
  });

  it('handles encoded query parameters', () => {
    expect(parseQueryParams('?name=John%20Doe&email=test%40example.com')).toEqual({
      name: 'John Doe',
      email: '<EMAIL>'
    });
  });
});

describe('Array Utilities', () => {
  const testData = [
    { id: 1, name: 'John', category: 'A', age: 30 },
    { id: 2, name: 'Jane', category: 'B', age: 25 },
    { id: 3, name: 'Bob', category: 'A', age: 35 },
    { id: 1, name: 'John', category: 'A', age: 30 }, // Duplicate
  ];

  it('sorts by property correctly', () => {
    const sorted = sortByProperty(testData, 'name');
    expect(sorted[0].name).toBe('Bob');
    expect(sorted[1].name).toBe('Jane');
    expect(sorted[2].name).toBe('John');
  });

  it('sorts by property in descending order', () => {
    const sorted = sortByProperty(testData, 'age', 'desc');
    expect(sorted[0].age).toBe(35);
    expect(sorted[1].age).toBe(30);
    expect(sorted[2].age).toBe(25);
  });

  it('groups by property correctly', () => {
    const grouped = groupByProperty(testData, 'category');
    expect(grouped.A).toHaveLength(3); // Including duplicate
    expect(grouped.B).toHaveLength(1);
    expect(grouped.A[0].name).toBe('John');
    expect(grouped.B[0].name).toBe('Jane');
  });

  it('removes duplicates correctly', () => {
    const unique = removeDuplicates(testData, 'id');
    expect(unique).toHaveLength(3);
    expect(unique.filter(item => item.id === 1)).toHaveLength(1);
  });
});

describe('Object Utilities', () => {
  it('deep clones objects correctly', () => {
    const original = {
      name: 'John',
      details: {
        age: 30,
        hobbies: ['reading', 'coding']
      }
    };
    
    const cloned = deepClone(original);
    
    expect(cloned).toEqual(original);
    expect(cloned).not.toBe(original);
    expect(cloned.details).not.toBe(original.details);
    expect(cloned.details.hobbies).not.toBe(original.details.hobbies);
  });

  it('checks if object is empty', () => {
    expect(isEmptyObject({})).toBe(true);
    expect(isEmptyObject({ name: 'John' })).toBe(false);
    expect(isEmptyObject(null)).toBe(true);
    expect(isEmptyObject(undefined)).toBe(true);
  });

  it('merges objects correctly', () => {
    const obj1 = { a: 1, b: 2 };
    const obj2 = { b: 3, c: 4 };
    const merged = mergeObjects(obj1, obj2);
    
    expect(merged).toEqual({ a: 1, b: 3, c: 4 });
    expect(merged).not.toBe(obj1);
    expect(merged).not.toBe(obj2);
  });

  it('merges nested objects correctly', () => {
    const obj1 = { user: { name: 'John', age: 30 } };
    const obj2 = { user: { age: 31, email: '<EMAIL>' } };
    const merged = mergeObjects(obj1, obj2, true); // Deep merge
    
    expect(merged.user).toEqual({
      name: 'John',
      age: 31,
      email: '<EMAIL>'
    });
  });
});

describe('Debounce Utility', () => {
  it('debounces function calls', (done) => {
    let callCount = 0;
    const debouncedFn = debounce(() => {
      callCount++;
    }, 100);

    // Call multiple times quickly
    debouncedFn();
    debouncedFn();
    debouncedFn();

    // Should not have been called yet
    expect(callCount).toBe(0);

    // Wait for debounce delay
    setTimeout(() => {
      expect(callCount).toBe(1);
      done();
    }, 150);
  });
});
