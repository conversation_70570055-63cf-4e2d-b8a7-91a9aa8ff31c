import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { ConfirmDialog } from '../ConfirmDialog';

describe('ConfirmDialog', () => {
  it('renders trigger element', () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        onConfirm={mockConfirm}
        trigger={<button>Delete Item</button>}
      />
    );
    
    expect(screen.getByText('Delete Item')).toBeInTheDocument();
  });

  it('opens dialog when trigger is clicked', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Delete"
        description="Are you sure?"
        onConfirm={mockConfirm}
        trigger={<button>Delete</button>}
      />
    );
    
    const trigger = screen.getByText('Delete');
    fireEvent.click(trigger);
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
      expect(screen.getByText('Are you sure?')).toBeInTheDocument();
    });
  });

  it('calls onConfirm when confirm button is clicked', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Delete"
        confirmText="Delete"
        onConfirm={mockConfirm}
        trigger={<button>Delete</button>}
      />
    );
    
    // Open dialog
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });
    
    // Click confirm
    const confirmButton = screen.getByRole('button', { name: /delete/i });
    fireEvent.click(confirmButton);
    
    expect(mockConfirm).toHaveBeenCalledTimes(1);
  });

  it('calls onCancel when cancel button is clicked', async () => {
    const mockConfirm = vi.fn();
    const mockCancel = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Delete"
        onConfirm={mockConfirm}
        onCancel={mockCancel}
        trigger={<button>Delete</button>}
      />
    );
    
    // Open dialog
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Delete')).toBeInTheDocument();
    });
    
    // Click cancel
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    
    expect(mockCancel).toHaveBeenCalledTimes(1);
    expect(mockConfirm).not.toHaveBeenCalled();
  });

  it('auto-generates title and description from itemName', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        itemName="Project Alpha"
        onConfirm={mockConfirm}
        trigger={<button>Delete</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      expect(screen.getByText('Delete Project Alpha')).toBeInTheDocument();
      expect(screen.getByText(/are you sure you want to delete "Project Alpha"/i)).toBeInTheDocument();
    });
  });

  it('uses custom title and description when provided', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Custom Title"
        description="Custom description text"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByText('Custom Title')).toBeInTheDocument();
      expect(screen.getByText('Custom description text')).toBeInTheDocument();
    });
  });

  it('uses custom button text', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        confirmText="Proceed"
        cancelText="Go Back"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /proceed/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /go back/i })).toBeInTheDocument();
    });
  });

  it('closes dialog after confirmation', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Action"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    // Open dialog
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    });
    
    // Confirm action
    const confirmButton = screen.getByRole('button', { name: /confirm/i });
    fireEvent.click(confirmButton);
    
    // Dialog should close
    await waitFor(() => {
      expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
    });
  });

  it('closes dialog after cancellation', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Action"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    // Open dialog
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    });
    
    // Cancel action
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);
    
    // Dialog should close
    await waitFor(() => {
      expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
    });
  });

  it('prevents event propagation on trigger click', () => {
    const mockConfirm = vi.fn();
    const mockParentClick = vi.fn();
    
    render(
      <div onClick={mockParentClick}>
        <ConfirmDialog
          onConfirm={mockConfirm}
          trigger={<button>Delete</button>}
        />
      </div>
    );
    
    fireEvent.click(screen.getByText('Delete'));
    
    expect(mockParentClick).not.toHaveBeenCalled();
  });

  it('prevents event propagation on dialog content click', async () => {
    const mockConfirm = vi.fn();
    const mockParentClick = vi.fn();
    
    render(
      <div onClick={mockParentClick}>
        <ConfirmDialog
          title="Confirm Action"
          onConfirm={mockConfirm}
          trigger={<button>Action</button>}
        />
      </div>
    );
    
    // Open dialog
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    });
    
    // Click on dialog content
    const dialogContent = screen.getByText('Confirm Action').closest('[role="alertdialog"]');
    if (dialogContent) {
      fireEvent.click(dialogContent);
      expect(mockParentClick).not.toHaveBeenCalled();
    }
  });
});

describe('ConfirmDialog Accessibility', () => {
  it('has proper ARIA attributes', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Delete"
        description="This action cannot be undone"
        onConfirm={mockConfirm}
        trigger={<button>Delete</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      const dialog = screen.getByRole('alertdialog');
      expect(dialog).toBeInTheDocument();
      expect(dialog).toHaveAttribute('aria-labelledby');
      expect(dialog).toHaveAttribute('aria-describedby');
    });
  });

  it('focuses on dialog when opened', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Action"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      const dialog = screen.getByRole('alertdialog');
      expect(dialog).toBeInTheDocument();
      // Focus management would need to be tested with more specific setup
    });
  });

  it('traps focus within dialog', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        title="Confirm Action"
        onConfirm={mockConfirm}
        trigger={<button>Action</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      
      expect(confirmButton).toBeInTheDocument();
      expect(cancelButton).toBeInTheDocument();
      
      // Focus trap testing would require more specific setup
    });
  });
});

describe('ConfirmDialog Edge Cases', () => {
  it('handles missing onConfirm gracefully', async () => {
    render(
      <ConfirmDialog
        title="Test"
        onConfirm={() => {}}
        trigger={<button>Action</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(screen.getByText('Test')).toBeInTheDocument();
    });
  });

  it('handles empty itemName', async () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        itemName=""
        onConfirm={mockConfirm}
        trigger={<button>Delete</button>}
      />
    );
    
    fireEvent.click(screen.getByText('Delete'));
    
    await waitFor(() => {
      expect(screen.getByText('Confirm Action')).toBeInTheDocument();
    });
  });

  it('handles complex trigger elements', () => {
    const mockConfirm = vi.fn();
    
    render(
      <ConfirmDialog
        onConfirm={mockConfirm}
        trigger={
          <div>
            <span>Complex</span>
            <button>Trigger</button>
          </div>
        }
      />
    );
    
    expect(screen.getByText('Complex')).toBeInTheDocument();
    expect(screen.getByText('Trigger')).toBeInTheDocument();
  });
});
